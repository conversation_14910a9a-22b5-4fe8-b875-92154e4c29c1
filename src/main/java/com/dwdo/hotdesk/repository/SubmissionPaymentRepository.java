package com.dwdo.hotdesk.repository;

import com.dwdo.hotdesk.model.SubmissionPayment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface SubmissionPaymentRepository extends JpaRepository<SubmissionPayment, Long>, JpaSpecificationExecutor<SubmissionPayment> {

    Optional<SubmissionPayment> findByReferenceNumber(String referenceNumber);

}
